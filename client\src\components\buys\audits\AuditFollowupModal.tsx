import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Flag, 
  CheckCircle, 
  X, 
  AlertTriangle,
  MessageSquare,
  Calendar
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { format } from 'date-fns';

interface AuditFollowupModalProps {
  audit: any;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (updatedAudit: any) => void;
  mode: 'flag' | 'complete' | 'unflag';
}

export function AuditFollowupModal({ 
  audit, 
  isOpen, 
  onClose, 
  onUpdate, 
  mode 
}: AuditFollowupModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    flagReason: audit.flagReason || '',
    followupResponse: audit.followupResponse || '',
    followupDate: format(new Date(), 'yyyy-MM-dd'),
    priority: 'medium',
    assignedTo: '',
    expectedCompletionDate: '',
  });

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      
      let updatedAudit = { ...audit };
      
      switch (mode) {
        case 'flag':
          if (!formData.flagReason.trim()) {
            toast({
              title: 'Error',
              description: 'Please provide a reason for flagging this audit.',
              variant: 'destructive',
            });
            return;
          }
          updatedAudit = {
            ...updatedAudit,
            flaggedForFollowup: true,
            flagReason: formData.flagReason,
            flagDate: new Date().toISOString(),
            followedUp: false,
            priority: formData.priority,
            assignedTo: formData.assignedTo,
            expectedCompletionDate: formData.expectedCompletionDate,
          };
          break;
          
        case 'complete':
          if (!formData.followupResponse.trim()) {
            toast({
              title: 'Error',
              description: 'Please provide a follow-up response.',
              variant: 'destructive',
            });
            return;
          }
          updatedAudit = {
            ...updatedAudit,
            followedUp: true,
            followupResponse: formData.followupResponse,
            followupDate: new Date(formData.followupDate).toISOString(),
            followupCompletedBy: 'Current User', // TODO: Get from auth context
          };
          break;
          
        case 'unflag':
          updatedAudit = {
            ...updatedAudit,
            flaggedForFollowup: false,
            followedUp: false,
            flagReason: '',
            followupResponse: '',
            unflaggedDate: new Date().toISOString(),
            unflaggedBy: 'Current User', // TODO: Get from auth context
          };
          break;
      }
      
      // TODO: Implement API call to update audit
      // await updateAuditFollowup(audit._id, updatedAudit);
      
      const actionText = mode === 'flag' ? 'flagged' : mode === 'complete' ? 'follow-up completed' : 'unflagged';
      toast({
        title: 'Success',
        description: `Audit has been ${actionText} successfully.`,
      });
      
      onUpdate(updatedAudit);
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: `Failed to ${mode} audit.`,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case 'flag':
        return 'Flag Audit for Follow-up';
      case 'complete':
        return 'Complete Follow-up';
      case 'unflag':
        return 'Unflag Audit';
      default:
        return 'Manage Follow-up';
    }
  };

  const getModalDescription = () => {
    switch (mode) {
      case 'flag':
        return 'Flag this audit for follow-up action. Provide details about what needs to be addressed.';
      case 'complete':
        return 'Mark the follow-up as complete. Provide details about the actions taken.';
      case 'unflag':
        return 'Remove the follow-up flag from this audit. This action should only be taken if the follow-up is no longer needed.';
      default:
        return 'Manage follow-up status for this audit.';
    }
  };

  const getModalIcon = () => {
    switch (mode) {
      case 'flag':
        return <Flag className="h-5 w-5 text-red-500" />;
      case 'complete':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'unflag':
        return <X className="h-5 w-5 text-gray-500" />;
      default:
        return <Flag className="h-5 w-5" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getModalIcon()}
            {getModalTitle()}
          </DialogTitle>
          <DialogDescription>
            {getModalDescription()}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Current Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Flagged for Follow-up</p>
                  <Badge variant={audit.flaggedForFollowup ? 'destructive' : 'secondary'}>
                    {audit.flaggedForFollowup ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Follow-up Status</p>
                  <Badge variant={audit.followedUp ? 'success' : 'secondary'}>
                    {audit.followedUp ? 'Completed' : 'Pending'}
                  </Badge>
                </div>
              </div>
              
              {audit.flagReason && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground">Current Flag Reason</p>
                  <p className="text-sm mt-1 p-2 bg-muted rounded">{audit.flagReason}</p>
                </div>
              )}
              
              {audit.followupResponse && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground">Previous Follow-up Response</p>
                  <p className="text-sm mt-1 p-2 bg-muted rounded">{audit.followupResponse}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Form Fields Based on Mode */}
          {mode === 'flag' && (
            <Card>
              <CardHeader>
                <CardTitle>Flag Details</CardTitle>
                <CardDescription>
                  Provide information about why this audit needs follow-up
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="flagReason">Reason for Follow-up *</Label>
                  <Textarea
                    id="flagReason"
                    value={formData.flagReason}
                    onChange={(e) => setFormData(prev => ({ ...prev, flagReason: e.target.value }))}
                    placeholder="Describe what needs to be addressed..."
                    className="min-h-[100px]"
                    required
                  />
                </div>
                
                <div>
                  <Label>Priority Level</Label>
                  <RadioGroup
                    value={formData.priority}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                    className="flex flex-row space-x-6 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="low" id="priority-low" />
                      <Label htmlFor="priority-low">Low</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="medium" id="priority-medium" />
                      <Label htmlFor="priority-medium">Medium</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="high" id="priority-high" />
                      <Label htmlFor="priority-high">High</Label>
                    </div>
                  </RadioGroup>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="assignedTo">Assigned To</Label>
                    <Input
                      id="assignedTo"
                      value={formData.assignedTo}
                      onChange={(e) => setFormData(prev => ({ ...prev, assignedTo: e.target.value }))}
                      placeholder="Person responsible for follow-up"
                    />
                  </div>
                  <div>
                    <Label htmlFor="expectedDate">Expected Completion</Label>
                    <Input
                      id="expectedDate"
                      type="date"
                      value={formData.expectedCompletionDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, expectedCompletionDate: e.target.value }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {mode === 'complete' && (
            <Card>
              <CardHeader>
                <CardTitle>Follow-up Completion</CardTitle>
                <CardDescription>
                  Document the actions taken to address the flagged issues
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="followupResponse">Follow-up Response *</Label>
                  <Textarea
                    id="followupResponse"
                    value={formData.followupResponse}
                    onChange={(e) => setFormData(prev => ({ ...prev, followupResponse: e.target.value }))}
                    placeholder="Describe the actions taken to address the issues..."
                    className="min-h-[120px]"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="followupDate">Completion Date</Label>
                  <Input
                    id="followupDate"
                    type="date"
                    value={formData.followupDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, followupDate: e.target.value }))}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {mode === 'unflag' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Confirm Unflag
                </CardTitle>
                <CardDescription>
                  Are you sure you want to remove the follow-up flag from this audit?
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <p className="text-sm text-orange-800">
                    <strong>Warning:</strong> Unflagging this audit will remove it from the follow-up queue. 
                    This action should only be taken if the follow-up is no longer needed or if it was flagged in error.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={isSubmitting}
            variant={mode === 'unflag' ? 'destructive' : 'default'}
          >
            {isSubmitting ? 'Processing...' : 
             mode === 'flag' ? 'Flag for Follow-up' :
             mode === 'complete' ? 'Complete Follow-up' :
             'Unflag Audit'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
