import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  History, 
  Clock, 
  User, 
  Edit, 
  Flag, 
  CheckCircle, 
  MessageSquare,
  FileText,
  AlertTriangle
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';

interface HistoryEntry {
  _id: string;
  auditId: string;
  action: string;
  description: string;
  userId: string;
  userName: string;
  userRole: string;
  timestamp: string;
  details?: any;
}

interface AuditHistoryViewProps {
  auditId: string;
}

export function AuditHistoryView({ auditId }: AuditHistoryViewProps) {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load audit history
  useEffect(() => {
    loadHistory();
  }, [auditId]);

  const loadHistory = async () => {
    try {
      setIsLoading(true);
      // TODO: Implement API call to fetch audit history
      // const response = await getAuditHistory(auditId);
      // setHistory(response.data);
      
      // Mock data for now
      setHistory([
        {
          _id: '1',
          auditId,
          action: 'created',
          description: 'Audit created',
          userId: 'user1',
          userName: 'John Auditor',
          userRole: 'manager',
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          _id: '2',
          auditId,
          action: 'updated',
          description: 'Pricing assessment updated',
          userId: 'user1',
          userName: 'John Auditor',
          userRole: 'manager',
          timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
          details: { section: 'pricing', status: 'fail' }
        },
        {
          _id: '3',
          auditId,
          action: 'flagged',
          description: 'Audit flagged for follow-up',
          userId: 'user2',
          userName: 'Sarah Manager',
          userRole: 'manager',
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          details: { reason: 'Pricing policy violation requires employee training' }
        },
        {
          _id: '4',
          auditId,
          action: 'comment_added',
          description: 'Comment added',
          userId: 'user2',
          userName: 'Sarah Manager',
          userRole: 'manager',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          details: { comment: 'Scheduled training session with employee' }
        },
        {
          _id: '5',
          auditId,
          action: 'followup_completed',
          description: 'Follow-up completed',
          userId: 'user2',
          userName: 'Sarah Manager',
          userRole: 'manager',
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          details: { response: 'Employee completed training and demonstrated understanding' }
        },
        {
          _id: '6',
          auditId,
          action: 'unflagged',
          description: 'Audit unflagged',
          userId: 'user3',
          userName: 'Mike Admin',
          userRole: 'admin',
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        }
      ]);
    } catch (error: any) {
      console.error('Failed to load audit history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'created':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'updated':
        return <Edit className="h-4 w-4 text-orange-500" />;
      case 'flagged':
        return <Flag className="h-4 w-4 text-red-500" />;
      case 'unflagged':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'followup_completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'comment_added':
        return <MessageSquare className="h-4 w-4 text-purple-500" />;
      default:
        return <History className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'created':
        return 'bg-blue-100 text-blue-800';
      case 'updated':
        return 'bg-orange-100 text-orange-800';
      case 'flagged':
        return 'bg-red-100 text-red-800';
      case 'unflagged':
      case 'followup_completed':
        return 'bg-green-100 text-green-800';
      case 'comment_added':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'manager':
        return 'bg-blue-100 text-blue-800';
      case 'employee':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Audit History
          </CardTitle>
          <CardDescription>
            Complete timeline of all actions and changes made to this audit
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Loading audit history...
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No history available for this audit.
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((entry, index) => (
                <div key={entry._id} className="relative">
                  {/* Timeline line */}
                  {index < history.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-border"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    {/* Action icon */}
                    <div className="flex-shrink-0 w-12 h-12 bg-background border-2 border-border rounded-full flex items-center justify-center">
                      {getActionIcon(entry.action)}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-2 flex-wrap">
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${getActionColor(entry.action)}`}
                        >
                          {entry.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </Badge>
                        <span className="text-sm font-medium">{entry.description}</span>
                      </div>
                      
                      <div className="flex items-center space-x-3 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Avatar className="h-5 w-5">
                            <AvatarFallback className="text-xs">
                              {getInitials(entry.userName)}
                            </AvatarFallback>
                          </Avatar>
                          <span>{entry.userName}</span>
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${getRoleColor(entry.userRole)}`}
                          >
                            {entry.userRole}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span title={format(new Date(entry.timestamp), 'PPpp')}>
                            {formatDistanceToNow(new Date(entry.timestamp), { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                      
                      {/* Additional details */}
                      {entry.details && (
                        <div className="text-xs bg-muted rounded-md p-2 mt-2">
                          {entry.details.reason && (
                            <div><strong>Reason:</strong> {entry.details.reason}</div>
                          )}
                          {entry.details.comment && (
                            <div><strong>Comment:</strong> {entry.details.comment}</div>
                          )}
                          {entry.details.response && (
                            <div><strong>Response:</strong> {entry.details.response}</div>
                          )}
                          {entry.details.section && (
                            <div><strong>Section:</strong> {entry.details.section} → {entry.details.status}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* History Statistics */}
      <Card className="border-dashed">
        <CardContent className="pt-6">
          <div className="text-sm text-muted-foreground space-y-2">
            <h4 className="font-medium text-foreground">History Summary:</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div>
                <span className="font-medium">Total Actions:</span> {history.length}
              </div>
              <div>
                <span className="font-medium">Updates:</span> {history.filter(h => h.action === 'updated').length}
              </div>
              <div>
                <span className="font-medium">Comments:</span> {history.filter(h => h.action === 'comment_added').length}
              </div>
              <div>
                <span className="font-medium">Follow-ups:</span> {history.filter(h => h.action.includes('follow')).length}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
