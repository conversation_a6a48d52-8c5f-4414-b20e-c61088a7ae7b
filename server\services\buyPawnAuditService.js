/**
 * Buy/Pawn Audit Service
 *
 * Handles operations related to buy/pawn audits, including:
 * - Creating and updating audits
 * - Retrieving and filtering audits
 * - Managing audit status and flags
 */

const BuyPawnAudit = require('../models/BuyPawnAudit');
const mongoose = require('mongoose');



/**
 * Get an audit by ID
 * @param {string} id - The audit ID
 * @returns {Promise<Object>} - The audit
 */
const getAuditById = async (id) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return { success: false, error: 'Invalid audit ID' };
    }

    const audit = await BuyPawnAudit.findById(id)
      .populate('auditedBy', 'username fullName');

    if (!audit) {
      return { success: false, error: 'Audit not found' };
    }

    return { success: true, data: audit };
  } catch (error) {
    console.error('Error getting buy/pawn audit by ID:', error);
    return { success: false, error: error.message };
  }
};



/**
 * Get an audit by transaction ID
 * @param {string} transactionId - The transaction ID
 * @returns {Promise<Object>} - The audit
 */
const getAuditByTransactionId = async (transactionId) => {
  try {
    const audit = await BuyPawnAudit.findOne({ transactionId })
      .populate('auditedBy', 'username fullName');

    if (!audit) {
      return { success: false, error: 'Audit not found for this transaction' };
    }

    return { success: true, data: audit };
  } catch (error) {
    console.error('Error getting buy/pawn audit by transaction ID:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update an audit
 * @param {string} id - The audit ID
 * @param {Object} updateData - The data to update
 * @param {Object} user - The user updating the audit
 * @returns {Promise<Object>} - The updated audit
 */
const updateAudit = async (id, updateData, user) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return { success: false, error: 'Invalid audit ID' };
    }

    // Get the audit
    const audit = await BuyPawnAudit.findById(id);
    if (!audit) {
      return { success: false, error: 'Audit not found' };
    }

    // Check if audit is already finalized
    if (audit.status === 'completed' && !updateData.flaggedForFollowup) {
      return { success: false, error: 'Cannot update a finalized audit' };
    }

    // Track changes for history
    const changes = {};
    Object.keys(updateData).forEach(key => {
      if (key !== '_id' && key !== 'dealId' && key !== 'transactionId' && key !== 'transactionType') {
        if (JSON.stringify(audit[key]) !== JSON.stringify(updateData[key])) {
          changes[key] = {
            from: audit[key],
            to: updateData[key]
          };
        }
        audit[key] = updateData[key];
      }
    });

    // If finalizing the audit, set finalizedAt
    if (updateData.status === 'completed' && audit.status !== 'completed') {
      audit.finalizedAt = new Date();

      // Calculate risk score
      audit.calculateRiskScore();

      // If essential item check is non-compliant, automatically fail compliance assessment
      if (audit.essentialItemCheck.compliance === 'non_compliant') {
        audit.complianceAssessment.status = 'fail';
      }
    }

    // If resolving a flag, set flagResolvedAt
    if (audit.flaggedForFollowup && !updateData.flaggedForFollowup) {
      audit.flagResolvedAt = new Date();
      audit.status = 'resolved';
    }

    // If flagging for followup, update status
    if (!audit.flaggedForFollowup && updateData.flaggedForFollowup) {
      audit.status = 'flagged';
    }

    // Add history entry if there were changes
    if (Object.keys(changes).length > 0) {
      if (!audit.history) {
        audit.history = [];
      }

      audit.history.push({
        _id: new mongoose.Types.ObjectId(),
        action: 'updated',
        description: `Audit updated - ${Object.keys(changes).join(', ')} modified`,
        details: {
          changes,
          fieldsChanged: Object.keys(changes)
        },
        performedBy: {
          _id: user._id,
          fullName: user.fullName,
          role: user.role
        },
        performedAt: new Date()
      });
    }

    audit.updatedAt = new Date();
    await audit.save();

    return { success: true, data: audit };
  } catch (error) {
    console.error('Error updating buy/pawn audit:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get audits with filtering and pagination
 * @param {Object} options - The filter options
 * @returns {Promise<Object>} - The audits and pagination info
 */
const getAudits = async (options) => {
  try {
    const {
      status,
      transactionType,
      riskLevel,
      auditedBy,
      flaggedForFollowup,
      search,
      startDate,
      endDate,
      page = 1,
      limit = 20,
      sort = 'createdAt',
      sortDirection = 'desc'
    } = options;

    // Build query
    const query = {};

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by transaction type
    if (transactionType) {
      query.transactionType = transactionType;
    }

    // Filter by risk level
    if (riskLevel) {
      query.riskLevel = riskLevel;
    }

    // Filter by auditor
    if (auditedBy) {
      query.auditedBy = auditedBy;
    }

    // Filter by flagged status
    if (flaggedForFollowup !== undefined) {
      query.flaggedForFollowup = flaggedForFollowup === 'true';
    }

    // Filter by date range
    if (startDate || endDate) {
      query.auditDate = {};
      if (startDate) {
        query.auditDate.$gte = new Date(startDate);
      }
      if (endDate) {
        query.auditDate.$lte = new Date(endDate);
      }
    }

    // Search by transaction ID
    if (search) {
      query.transactionId = { $regex: search, $options: 'i' };
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build sort
    const sortObj = {};
    sortObj[sort] = sortDirection === 'asc' ? 1 : -1;

    // Get total count
    const total = await BuyPawnAudit.countDocuments(query);

    // Get audits
    const audits = await BuyPawnAudit.find(query)
      .populate('auditedBy', 'username fullName')
      .sort(sortObj)
      .skip(skip)
      .limit(parseInt(limit));

    return {
      success: true,
      data: audits,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    };
  } catch (error) {
    console.error('Error getting buy/pawn audits:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create a new audit directly with transaction details
 * @param {Object} auditData - The audit data
 * @param {Object} user - The user creating the audit
 * @returns {Promise<Object>} - The created audit
 */
const createDirectAudit = async (auditData, user) => {
  try {
    console.log('Creating audit with data:', JSON.stringify(auditData, null, 2));

    // Destructure all the data from auditData
    const {
      auditType,
      transactionType,
      transactionId,
      employeeId,
      employeeName,
      amount,
      transactionDate,
      auditDate,
      itemDescription,
      stockcode,
      brand,
      items,
      dataEntryQuality,
      itemConditionCheck,
      pricing,
      authorizedLimitCheck,
      polSuitability,
      customerUnderstanding,
      vulnerableCustomer,
      essentialItemCheck,
      overallCompliance,
      auditNotes,
      overallScore,
      flaggedForFollowup,
      flagReason
    } = auditData;

    // Validate required fields
    if (!transactionId || !employeeId || !amount) {
      return { success: false, error: 'Missing required fields: transactionId, employeeId, amount' };
    }

    // Validate dates
    if (!transactionDate || !auditDate) {
      return { success: false, error: 'Missing required dates: transactionDate, auditDate' };
    }

    // Parse and validate dates
    const parsedTransactionDate = new Date(transactionDate);
    const parsedAuditDate = new Date(auditDate);

    if (isNaN(parsedTransactionDate.getTime()) || isNaN(parsedAuditDate.getTime())) {
      return { success: false, error: 'Invalid date format provided' };
    }

    // Check if an audit already exists for this transaction
    const existingAudit = await BuyPawnAudit.findOne({ transactionId });
    if (existingAudit) {
      return { success: false, error: 'An audit already exists for this transaction' };
    }

    // Prepare audit data object
    const auditDataToSave = {
      auditType: auditType || transactionType, // Use auditType if provided, fallback to transactionType
      transactionId,
      employeeId,
      employeeName,
      amount,
      transactionDate: parsedTransactionDate,
      auditDate: parsedAuditDate,
      itemDescription,
      stockcode: stockcode || '',
      brand: brand || '',
      auditedBy: {
        _id: user._id,
        fullName: user.fullName
      },
      status: 'draft',
      overallCompliance: overallCompliance || 'not_assessed',
      overallScore: overallScore || 0,
      auditNotes: auditNotes || '',
      flaggedForFollowup: flaggedForFollowup || false,
      flagReason: flagReason || ''
    };

    // Add items if provided (new multi-item structure)
    if (items && Array.isArray(items) && items.length > 0) {
      auditDataToSave.items = items;
    }

    // Add assessment sections with proper defaults
    auditDataToSave.dataEntryQuality = dataEntryQuality || { status: 'not_assessed', failReasons: [], notes: '' };
    auditDataToSave.itemConditionCheck = itemConditionCheck || { status: 'not_assessed', failReasons: [], notes: '' };
    auditDataToSave.authorizedLimitCheck = authorizedLimitCheck || { status: 'not_assessed', failReasons: [], notes: '' };

    // Handle pricing section with special validation for price audits
    if (pricing) {
      auditDataToSave.pricing = pricing;
    } else {
      auditDataToSave.pricing = {
        status: 'not_assessed',
        failReasons: [],
        suggestedPrice: '',
        costPrice: '',
        ticketPrice: '',
        notes: ''
      };
    }

    // Add pawn-specific sections if audit type is pawn
    if (auditDataToSave.auditType === 'pawn') {
      auditDataToSave.polSuitability = polSuitability || { status: 'not_assessed', polText: '', failReasons: [], notes: '' };
      auditDataToSave.customerUnderstanding = customerUnderstanding || { status: 'not_assessed', failReasons: [], notes: '' };
      auditDataToSave.vulnerableCustomer = vulnerableCustomer || { status: 'not_assessed', failReasons: [], notes: '' };
      auditDataToSave.essentialItemCheck = essentialItemCheck || {
        status: 'not_assessed',
        isEssential: false,
        compliance: 'not_assessed',
        failReasons: [],
        notes: ''
      };
    }

    // Add initial history entry
    auditDataToSave.history = [{
      _id: new mongoose.Types.ObjectId(),
      action: 'created',
      description: `Audit created for ${auditDataToSave.auditType} transaction ${transactionId}`,
      details: {
        auditType: auditDataToSave.auditType,
        transactionId,
        amount,
        employeeName
      },
      performedBy: {
        _id: user._id,
        fullName: user.fullName,
        role: user.role
      },
      performedAt: new Date()
    }];

    // Initialize empty comments array
    auditDataToSave.comments = [];

    console.log('Final audit data to save:', JSON.stringify(auditDataToSave, null, 2));

    // Create new audit
    const audit = new BuyPawnAudit(auditDataToSave);
    await audit.save();

    return { success: true, data: audit };
  } catch (error) {
    console.error('Error creating direct buy/pawn audit:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Flag an audit for follow-up
 * @param {string} auditId - The audit ID
 * @param {string} flagReason - Reason for flagging
 * @param {Object} user - User performing the action
 * @returns {Object} Result object
 */
const flagAudit = async (auditId, flagReason, user) => {
  try {
    const audit = await BuyPawnAudit.findById(auditId);

    if (!audit) {
      return {
        success: false,
        error: 'Audit not found'
      };
    }

    audit.flaggedForFollowup = true;
    audit.flagReason = flagReason;
    audit.followedUp = false;
    audit.followupNotes = null;
    audit.flagResolvedAt = null;
    audit.flagResolvedBy = null;
    audit.updatedAt = new Date();

    // Add history entry
    if (!audit.history) {
      audit.history = [];
    }

    audit.history.push({
      _id: new mongoose.Types.ObjectId(),
      action: 'flagged',
      description: `Audit flagged for follow-up: ${flagReason}`,
      details: {
        flagReason,
        flaggedBy: user.fullName
      },
      performedBy: {
        _id: user._id,
        fullName: user.fullName,
        role: user.role
      },
      performedAt: new Date()
    });

    await audit.save();

    return {
      success: true,
      message: 'Audit flagged for follow-up successfully',
      data: audit
    };
  } catch (error) {
    console.error('Error flagging audit:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Remove flag from an audit
 * @param {string} auditId - The audit ID
 * @param {Object} user - User performing the action
 * @returns {Object} Result object
 */
const unflagAudit = async (auditId, user) => {
  try {
    const audit = await BuyPawnAudit.findById(auditId);

    if (!audit) {
      return {
        success: false,
        error: 'Audit not found'
      };
    }

    audit.flaggedForFollowup = false;
    audit.flagReason = null;
    audit.followedUp = false;
    audit.followupNotes = null;
    audit.flagResolvedAt = null;
    audit.flagResolvedBy = null;
    audit.updatedAt = new Date();

    // Add history entry
    if (!audit.history) {
      audit.history = [];
    }

    audit.history.push({
      _id: new mongoose.Types.ObjectId(),
      action: 'unflagged',
      description: 'Audit flag removed',
      details: {
        unflaggedBy: user.fullName
      },
      performedBy: {
        _id: user._id,
        fullName: user.fullName,
        role: user.role
      },
      performedAt: new Date()
    });

    await audit.save();

    return {
      success: true,
      message: 'Audit unflagged successfully',
      data: audit
    };
  } catch (error) {
    console.error('Error unflagging audit:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Complete follow-up for a flagged audit
 * @param {string} auditId - The audit ID
 * @param {string} followupNotes - Notes about the follow-up action
 * @param {Object} user - User performing the action
 * @returns {Object} Result object
 */
const completeFollowup = async (auditId, followupNotes, user) => {
  try {
    const audit = await BuyPawnAudit.findById(auditId);

    if (!audit) {
      return {
        success: false,
        error: 'Audit not found'
      };
    }

    if (!audit.flaggedForFollowup) {
      return {
        success: false,
        error: 'Audit is not flagged for follow-up'
      };
    }

    audit.followedUp = true;
    audit.followupNotes = followupNotes;
    audit.flagResolvedAt = new Date();
    audit.flagResolvedBy = {
      _id: user._id,
      fullName: user.fullName
    };
    audit.updatedAt = new Date();

    // Add history entry
    if (!audit.history) {
      audit.history = [];
    }

    audit.history.push({
      _id: new mongoose.Types.ObjectId(),
      action: 'followup_completed',
      description: `Follow-up completed: ${followupNotes.substring(0, 50)}${followupNotes.length > 50 ? '...' : ''}`,
      details: {
        followupNotes,
        completedBy: user.fullName
      },
      performedBy: {
        _id: user._id,
        fullName: user.fullName,
        role: user.role
      },
      performedAt: new Date()
    });

    await audit.save();

    return {
      success: true,
      message: 'Follow-up completed successfully',
      data: audit
    };
  } catch (error) {
    console.error('Error completing follow-up:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = {
  createDirectAudit,
  getAuditById,
  getAuditByTransactionId,
  updateAudit,
  getAudits,
  flagAudit,
  unflagAudit,
  completeFollowup
};
